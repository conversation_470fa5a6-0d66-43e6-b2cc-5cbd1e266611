// services/authApi.ts

import { CreateEmployeeRequestDef } from "@/models/request/employee/create-employee.request";
import { DefaultResponse } from "@/models/response/default.response";
import { EmployeeResponseDef } from "@/models/response/employee";
import { baseService } from "./base.service";

export const employeeService = baseService.injectEndpoints({
  endpoints: (builder) => ({
    createEmployee: builder.mutation<DefaultResponse, CreateEmployeeRequestDef>(
      {
        query: (credentials) => ({
          url: "/employee",
          method: "POST",
          body: credentials,
        }),
        invalidatesTags: ["Employees"],
      }
    ),
    createBulkEmployee: builder.mutation<
      DefaultResponse,
      CreateEmployeeRequestDef[]
    >({
      query: (credentials) => ({
        url: "/employee/create/bulk",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Employees"],
    }),
    getEmployees: builder.query<
      DefaultResponse & { data: EmployeeResponseDef[] },
      void
    >({
      query: () => ({
        url: "/employee",
        method: "GET",
      }),
      providesTags: ["Employees"],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateEmployeeMutation,
  useGetEmployeesQuery,
  useCreateBulkEmployeeMutation,
} = employeeService;
