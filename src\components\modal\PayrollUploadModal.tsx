/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAppDispatch } from "@/hooks";
import { useCreatePayrollMutation } from "@/services/payroll.service";
import { ColDef } from "ag-grid-community";
import { Trash2Icon } from "lucide-react";
import Papa from "papaparse";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import BaseTable from "../table/BaseTable";
import { Button } from "../ui/button";
import { DialogDescription, DialogHeader, DialogTitle } from "../ui/dialog";
import { Input } from "../ui/input";

import { modal } from "@/store/module/modal";
import { format } from "date-fns";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

const requiredFields = ["staffCode"];

const columnFields = [
  "staffCode",
  "fullName",
  "unit",
  "gradeLevel",
  "grossPay",
  "daysWorkedPreviousPayroll",
  "daysWorkedCurrentPayroll",
  "apprenticeAllowance",
  "specialCategoryAllowance",
  "lunchSubsidyAllowance",
  "monthlyBasicSalary",
  "housingAllowance",
  "transportAllowance",
  "utilityAllowance",
  "selfMaintenance",
  "furnitureAllowance",
  "hazardAllowance",
  "levelProficiency",
  "fuelSubsidy",
  "childEducationSubsidy",
  "domesticStaff",
  "responsibility",
  "managementFee",
  "grossPayAlt",
  "amortisedGross",
  "vehicleAmortisation",
  "leaveAllowance",
  "performanceBonus",
  "inconvenienceAllowance",
  "overTime",
  "outstandingSalary",
  "iouRecovery",
  "loanSalaryAdvanceDeduction",
  "productCashShortage",
  "lateness",
  "absenteeism",
  "otherPenalty",
  "otherDeduction",
  "cooperativeContribution",
  "pension",
  "paye",
  "grossPayable",
  "amortisedPaye",
  "totalDeduction",
  "netPay",
  "annualGross",
  "annualPension",
  "otherReliefs",
  "consolidatedRelief",
  "taxableIncome",
  "monthlyTax",
];

const PayrollUploadModal = () => {
  const dispatch = useAppDispatch();

  const [createPayroll, { isLoading }] = useCreatePayrollMutation();
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null);
  const [selectedYear, setSelectedYear] = useState<number | null>(null);

  const [payrollData, setPayrollData] = useState<any[]>([]);
  const [error, setError] = useState<string | undefined>(undefined);

  const columnDefs = columnFields.map((key) => ({
    headerName: key,
    field: key,
    sortable: true,
    filter: true,
    resizable: true,
  }));
  const [colDefs] = useState<ColDef<any>[]>(columnDefs);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(undefined);
    const file = event.target.files?.[0];
    if (!file) return;

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (result) => {
        const rawRows = result.data as any[];
        const uploadedHeaders = result.meta.fields ?? [];

        const missingHeaders = requiredFields.filter(
          (field) => !uploadedHeaders.includes(field)
        );

        if (missingHeaders.length > 0) {
          setError(`Missing required columns: ${missingHeaders.join(", ")}`);
          return;
        }

        const rows = rawRows.filter((row) => {
          return columnFields.some((field) => {
            const value = row[field];
            return value !== undefined && String(value).trim() !== "";
          });
        });

        if (!rows.length) {
          toast.error("No valid data rows found in CSV");
          return;
        }

        const textFields = ["staffCode", "fullName", "unit", "gradeLevel"];

        const sanitizedRows = rows.map((row) => {
          const sanitizedRow: Record<string, any> = {};

          columnFields.forEach((field) => {
            const value = row[field] ? row[field] : "-";

            if (textFields.includes(field)) {
              sanitizedRow[field] = value ?? "";
            } else {
              const numeric = parseFloat(
                String(value.trim() === "-" ? 0 : value)
                  .replace(/,/g, "")
                  .trim()
              );
              sanitizedRow[field] = isNaN(numeric) ? 0 : numeric;
            }
          });

          return sanitizedRow;
        });

        setPayrollData(sanitizedRows);
      },
    });
  };

  const handleSubmit = async () => {
    if (!payrollData.length) {
      toast.error("No data to upload");
      return;
    }

    const res = await createPayroll({
      period: `${selectedYear}-${selectedMonth?.toString().padStart(2, "0")}`,
      records: payrollData,
    }).unwrap();

    if (res.success) {
      toast("Payroll request pending authorization!");
      dispatch(modal.mutation.close());
    }

    setPayrollData([]);
  };

  const months = useMemo(
    () =>
      Array.from({ length: 12 }, (_, i) => format(new Date(2000, i), "MMMM")),
    []
  );

  const currentYear = new Date().getFullYear();
  const years = Array.from(
    { length: currentYear - 1900 + 1 },
    (_, i) => 1900 + i
  );

  const handleDownloadTemplate = () => {
    const link = document.createElement("a");
    link.href = "/templates/payroll-template.csv";
    link.setAttribute("download", "payroll-template.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDelete = (rowIndex: number) => {
    const updatedData = [...payrollData];
    updatedData.splice(rowIndex, 1);
    setPayrollData(updatedData);
  };

  const handleClose = () => {
    setPayrollData([]);
  };

  return (
    <div className="sm:min-w-xl lg:min-w-2xl">
      <DialogHeader className="sticky top-0 !bg-white z-10 flex pt-6">
        <DialogTitle>Payroll Upload</DialogTitle>
        <DialogDescription className="max-w-sm">
          <p>Upload a `.csv` file with employee information.</p>
          <div className="flex flex-col gap-4 mt-2 pb-2">
            <div className="flex gap-2">
              <Select
                value={selectedYear?.toString()}
                onValueChange={(value) => setSelectedYear(parseInt(value))}
              >
                <SelectTrigger className="w-[100px]">
                  <SelectValue placeholder={currentYear.toString()} />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={selectedMonth?.toString()}
                onValueChange={(value) => setSelectedMonth(parseInt(value))}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder={"Month"} />
                </SelectTrigger>
                <SelectContent>
                  {months.map((month, i) => (
                    <SelectItem key={month} value={i.toString()}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Input
              type="file"
              accept=".csv"
              onChange={handleFileUpload}
              disabled={!selectedMonth || !selectedYear}
            />
          </div>

          <Button
            onClick={handleDownloadTemplate}
            variant="link"
            className="px-0 cursor-pointer"
          >
            Download Template
          </Button>
        </DialogDescription>
      </DialogHeader>

      <div className="w-full">
        {error && (
          <div>
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}
        {payrollData.length > 0 && colDefs.length > 0 && (
          <div className="mt-4 max-h-[400px] overflow-auto border rounded">
            <BaseTable
              rowData={payrollData}
              columnDefs={colDefs}
              setPaginationPageSize={() => {}}
              showCustomPagination={false}
              actionOptions={{
                showDefault: true,
                actions: [
                  {
                    title: "Delete",
                    Icon: Trash2Icon,
                    onClick: (node) => {
                      handleDelete(node.rowIndex!);
                    },
                  },
                ],
              }}
            />
          </div>
        )}
      </div>

      {payrollData.length > 0 && colDefs.length > 0 && (
        <div className="flex justify-end gap-2 mt-4">
          <Button
            variant="outline"
            disabled={isLoading}
            size="sm"
            onClick={handleClose}
          >
            Cancel
          </Button>
          <Button disabled={isLoading} size="sm" onClick={handleSubmit}>
            Submit
          </Button>
        </div>
      )}
    </div>
  );
};

export default PayrollUploadModal;
