import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { camelToSnake, cn, copyToClipboard, isValidJSON } from "@/lib/utils";
import { sheet } from "@/store/module/sheet";
import { CheckIcon, CopyIcon, PenIcon } from "lucide-react";
import { useState } from "react";
import ReactJson from "react-json-view";
import { Button } from "../ui/button";
import { ScrollArea, ScrollBar } from "../ui/scroll-area";
import { SheetHeader, SheetTitle } from "../ui/sheet";

const EntityViewSheet = () => {
  const dispatch = useAppDispatch();
  const [copiedKey, setCopiedKey] = useState<string | null>(null);

  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  if (!metadata?.data || metadata?.data === undefined) {
    return (
      <div>
        <p>Data not found</p>
      </div>
    );
  }

  return (
    <>
      <SheetHeader>
        <div className="flex items-center gap-2">
          <SheetTitle className="font-medium text-left !text-base">
            Details
          </SheetTitle>
          {metadata?.sheetType === SheetConstant.authorizationQueue ? (
            <Button
              size="sm"
              onClick={() =>
                dispatch(
                  sheet.mutation.open({
                    component: SheetConstant.authorizationReviewSheet,
                    metadata: {
                      data: metadata.data,
                    },
                  })
                )
              }
            >
              Review Request
            </Button>
          ) : metadata?.formType ? (
            <Button
              size="icon"
              variant="ghost"
              onClick={() =>
                dispatch(
                  sheet.mutation.open({
                    component: SheetConstant.formSheet,
                    metadata: {
                      formType: metadata?.formType,
                      title: metadata?.title,
                    },
                  })
                )
              }
            >
              <PenIcon />
            </Button>
          ) : null}
        </div>
      </SheetHeader>
      <ScrollArea className="h-[calc(100%-5rem)] border-t">
        <div className="p-4">
          <div className={cn(" gap-5  mt-4 grid")}>
            {Object.entries(metadata?.data).map(([key, value]) => (
              <div
                key={key}
                className={cn(
                  key.toLocaleLowerCase().includes("data") && "col-span-2"
                )}
              >
                <p className="text-xs font-medium text-neutral-500 uppercase">
                  {camelToSnake(key).replaceAll("_", " ")}
                </p>
                <p className={cn("text-sm flex items-center gap-2")}>
                  {value !== null ? (
                    key.toLocaleLowerCase().includes("data") ? (
                      isValidJSON(value as string) ? (
                        <ReactJson
                          displayDataTypes={false}
                          src={JSON.parse(value as string)}
                        />
                      ) : (
                        value?.toString()
                      )
                    ) : (
                      value?.toString()
                    )
                  ) : (
                    "N/A"
                  )}
                  <Button
                    size="icon"
                    variant="ghost"
                    className="w-auto h-auto"
                    onClick={() => {
                      copyToClipboard(value ? value.toString() : "N/A");
                      setCopiedKey(key);
                      setTimeout(() => setCopiedKey(null), 2000);
                    }}
                  >
                    {copiedKey === key ? (
                      <CheckIcon className="text-green-600" />
                    ) : (
                      <CopyIcon />
                    )}
                  </Button>
                </p>
              </div>
            ))}
          </div>
        </div>
        <ScrollBar />
      </ScrollArea>
    </>
  );
};

export default EntityViewSheet;
