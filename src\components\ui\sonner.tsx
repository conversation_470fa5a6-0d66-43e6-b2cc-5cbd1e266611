import { useTheme } from 'next-themes';
import { Toaster as Sonner, ToasterProps } from 'sonner';

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = 'system' } = useTheme();

  return (
    <Sonner
      theme={theme as ToasterProps['theme']}
      className="toaster group"
      style={
        {
          // '--normal-bg': 'var(--popover)',
          // '--normal-text': 'var(--popover-foreground)',
          // '--normal-border': 'var(--border)',
          '--normal-bg': 'hsl(142.1 76.2% 36.3%)',
          '--normal-text': 'hsl(355.7 100% 97.3%)',
          '--normal-border': 'hsl(142.1 76.2% 36.3%)',
        } as React.CSSProperties
      }
      {...props}
    />
  );
};

export { Toaster };
