import { useAppDispatch } from "@/hooks";
import {
  CreateSubBranchDef,
  CreateSubBranchSchema,
} from "@/models/validations/sub-branch/create-sub-branch.validation";
import { useGetBranchesQuery } from "@/services/branch.service";
import { useCreateSubBranchMutation } from "@/services/sub-branch.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { ComboBoxField, InputField, TextareaField } from "../form-fields";
import { Button } from "../ui/button";
import { Form } from "../ui/form";

const CreateSubBranchForm = () => {
  const dispatch = useAppDispatch();

  const { data: branches, isLoading: isLoadingBranches } = useGetBranchesQuery(
    undefined,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  const [createSubBranch, { isLoading: isCreatingEmployee, error }] =
    useCreateSubBranchMutation();

  const form = useForm<CreateSubBranchDef>({
    resolver: zodResolver(CreateSubBranchSchema),
    defaultValues: {},
    mode: "all",
  });

  const onSubmit = async (data: CreateSubBranchDef) => {
    try {
      const res = await createSubBranch(data).unwrap();

      if (res.success) {
        toast("Request submitted and is pending authorization");
        dispatch(modal.mutation.close());
      }
    } catch {
      console.log(error);

      // toast(error)
    }
  };

  const selectedBranch = form.watch("branch");

  console.log(form.formState.errors);

  return (
    <section className="max-w-md mx-auto min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <>
              <ComboBoxField
                options={branches?.data || []}
                form={form}
                name="branch"
                label="Branch"
                disabled={isLoadingBranches}
                isLoading={isLoadingBranches}
                placeholder="Select branch"
                labelKey="name"
                valueKey="name"
              />

              <InputField
                form={form}
                name="name"
                label="Name"
                disabled={!selectedBranch}
              />

              <TextareaField
                form={form}
                name="description"
                label="Description "
                placeholder="Enter unit description"
                disabled={!selectedBranch}
              />

              <div className="flex gap-4 justify-between">
                <Button
                  className="font-medium"
                  disabled={isCreatingEmployee || !selectedBranch}
                >
                  {isCreatingEmployee && (
                    <LoaderIcon className="animate-spin" />
                  )}
                  Create Sub-Branch
                </Button>
              </div>
            </>
          </div>
        </form>
      </Form>
    </section>
  );
};

export default CreateSubBranchForm;
