import { RouteConstant } from "@/constants/RouteConstant";
import { useAppDispatch } from "@/hooks";
import { useGetAccountDataQuery } from "@/services/account.service";
import { account } from "@/store/module/account";
import { company } from "@/store/module/company";
import { skipToken } from "@reduxjs/toolkit/query";
import { useSearchParams } from "react-router-dom";
import AuthFormHeader from "../sections/AuthFormHeader";
import { Button } from "../ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import IamLoginForm from "./IamLoginForm";
import RootLoginForm from "./RootLoginForm";

const LoginForm = () => {
  const dispatch = useAppDispatch();

  const [searchParams, setSearchParams] = useSearchParams();

  const slug = window.location.hostname.split(".");

  const cid = searchParams.get("cid");

  const { data } = useGetAccountDataQuery(
    slug ? { slug: slug[0] } : skipToken,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  if (data) {
    dispatch(account.mutation.setAccount(data.data));
  }

  const handleCompanySelect = (companyId: string) => {
    const newParam = new URLSearchParams(searchParams);

    const selectedCompany = data?.data.companies.find(
      (comp) => comp.id === companyId
    );
    if (selectedCompany) {
      dispatch(company.mutation.setCompany(selectedCompany));
      newParam.set("cid", companyId);

      localStorage.setItem("cid", companyId);

      setSearchParams(newParam);
    }
  };

  return (
    <section className="max-w-md mx-auto min-h-screen place-content-center">
      {!cid && (
        <>
          <div>
            {data?.data ? (
              <>
                <AuthFormHeader
                  title="Welcome back to your account"
                  description="Select a business to continue"
                  showImg={false}
                />
                <div>
                  {data.data.companies.map((company) => (
                    <Button
                      key={company.id}
                      className="cursor-pointer text-center mb-4 border rounded-md overflow-clip flex gap-2 p-4 items-center w-full justify-start h-auto"
                      variant="outline"
                      onClick={() => handleCompanySelect(company.id)}
                    >
                      <img
                        src={company.logo}
                        alt={company.name}
                        className="w-10 h-10"
                      />
                      <p className="text-base font-semibold">{company.name}</p>
                    </Button>
                  ))}
                </div>
              </>
            ) : (
              <div>
                <AuthFormHeader
                  title="Account not found"
                  // description="Select a business to continue"
                  showImg={false}
                />
                <div className="flex flex-col items-center">
                  <Button
                    onClick={() => {
                      window.location.replace(
                        `${window.location.protocol}//${
                          window.location.host.includes("localhost")
                            ? "localhost:5173"
                            : "payroll.corestepmfb.com"
                        }${RouteConstant.auth.createAccount}`
                      );
                    }}
                  >
                    Create an Account
                  </Button>
                </div>
              </div>
            )}
          </div>
        </>
      )}
      {cid && (
        <>
          <AuthFormHeader
            title="Log in to your account"
            description="Welcome back! Please enter your details."
          />

          <Tabs defaultValue="root">
            <TabsList>
              <TabsTrigger value="root">Root User</TabsTrigger>
              <TabsTrigger value="iam">Staff</TabsTrigger>
            </TabsList>
            <TabsContent value="root">
              <RootLoginForm cid={cid} />
            </TabsContent>
            <TabsContent value="iam">
              <IamLoginForm cid={cid} />
            </TabsContent>
          </Tabs>
        </>
      )}
    </section>
  );
};

export default LoginForm;
