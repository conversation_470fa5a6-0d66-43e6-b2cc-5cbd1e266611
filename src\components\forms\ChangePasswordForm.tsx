import { useAppDispatch } from "@/hooks";
import {
  ChangePasswordDef,
  ChangePasswordSchema,
} from "@/models/validations/auth/change-password.validation";
import { useChangePasswordMutation } from "@/services/auth.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { InputField } from "../form-fields";
import { Button } from "../ui/button";
import { Form } from "../ui/form";

const ChangePasswordForm = () => {
  const dispatch = useAppDispatch();
  const [changePassword, { isLoading }] = useChangePasswordMutation();

  const form = useForm<ChangePasswordDef>({
    resolver: zodResolver(ChangePasswordSchema),
    defaultValues: {
      // username: account?.userEmail,
    },
    mode: "all",
  });

  // Reset email and password when the user type changes

  const onSubmit = async (data: ChangePasswordDef) => {
    const { oldPassword, newPassword } = data;
    const res = await changePassword({
      oldPassword,
      newPassword,
    }).unwrap();

    if (res.success) {
      toast.success("Password changed successfully");
      dispatch(modal.mutation.close());
    }
  };

  return (
    <section className="max-w-md mx-auto place-content-center sm:min-w-lg">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4  p-4">
          <div className="space-y-4">
            <InputField
              form={form}
              name="oldPassword"
              label="Old Password"
              type="password"
              placeholder="Enter your current password"
            />
            <InputField
              form={form}
              name="newPassword"
              label="New Password"
              type="password"
              placeholder="••••••••"
            />
            <InputField
              form={form}
              name="confirmPassword"
              label="Confirm New Password"
              type="password"
              placeholder="••••••••"
            />
          </div>

          <Button
            className="w-full font-medium !bg-primary"
            variant="default"
            disabled={isLoading}
          >
            {isLoading && <LoaderIcon className="animate-spin" />}
            Change Password
          </Button>
        </form>
      </Form>
    </section>
  );
};

export default ChangePasswordForm;
