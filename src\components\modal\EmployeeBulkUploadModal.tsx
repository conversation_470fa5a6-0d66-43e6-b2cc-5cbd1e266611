/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAppDispatch } from "@/hooks";
import { useCreateBulkEmployeeMutation } from "@/services/employee.service";
import { modal } from "@/store/module/modal";
import { ColDef } from "ag-grid-community";
import { Trash2Icon } from "lucide-react";
import Papa from "papaparse";
import { useRef, useState } from "react";
import { toast } from "sonner";
import BaseTable from "../table/BaseTable";
import { Input } from "../ui/input";

const columnFields = [
  "title",
  "gradeLevelName",
  "unitName",
  "contractTypeName",
  "staffCode",
  "firstName",
  "lastName",
  "middleName",
  "birthday",
  "gender",
  "maritalStatus",
  "nationality",
  "stateOfOrigin",
  "localGovt",
  "residentialAddress",
  "residentialLocalGovt",
  "residentialState",
  "residentialCountry",
  "phone1",
  "phone2",
  "email",
  "placeOfBirth",
  "religion",
  "nextOfKinFullName",
  "nextOfKinRelationship",
  "nextOfKinPhoneNumber",
  "nextOfKinEmail",
  "nextOfKinAddress",
  "highestQualification",
  "course",
  "institutionName",
  "dateOfGraduation",
  "dateEmployed",
  "bvn",
  "nin",
  "kycVerified",
  "nameOfSpouse",
  "noOfChildren",
  "accountNumber",
  "payeId",
  "pensionId",
  "pfa",
  "dateAppointedToLevel",
  "passport",
  "certificate",
  "guarantorPassport",
  "guarantorFullname",
  "guarantorPhoneNumber",
  "guarantorRelationShip",
  "guarantorAddress",
  "guarantorOccupation",
  "guarantorMeansOfIdentification",
  "branchName",
  "jobTitleName",
  "jobGradeName",
  "departmentName",
  "salaryPackageName",
];

const requiredFields = [
  "title",
  "firstName",
  "lastName",
  "birthday",
  "gender",
  "stateOfOrigin",
  "localGovt",
  "residentialAddress",
  "residentialLocalGovt",
  "residentialState",
  "residentialCountry",
  "phone1",
  "email",
  "placeOfBirth",
  "religion",
  "nextOfKinFullName",
  "nextOfKinRelationship",
  "nextOfKinPhoneNumber",
  "nextOfKinAddress",
  "bvn",
  "nin",
  "branchName",
  "jobTitleName",
  "salaryPackageName",
];

// const optionalFields = [
//   "dateAppointedToLevel",
//   "dateEmployed",
//   "nationality",
//   "maritalStatus",
//   "staffCode",
//   "middleName",
//   "phone2",
//   "nextOfKinEmail",
//   "highestQualification",
//   "course",
//   "institutionName",
//   "dateOfGraduation",
//   "nameOfSpouse",
//   "noOfChildren",
//   "taxId", // mapped as payeId?
//   "pensionId",
//   "pfa",
//   "accountNumber",
//   "passport",
//   "certificate",
//   "guarantorPassport",
//   "guarantorFullname",
//   "guarantorPhoneNumber",
//   "guarantorRelationShip",
//   "guarantorAddress",
//   "guarantorOccupation",
//   "guarantorMeansOfIdentification",
//   "gradeLevelName",
//   "departmentName",
//   "jobGradeName",
//   "unitName",
//   "contractTypeName",
// ];

const EmployeeBulkUploadModal = () => {
  const dispatch = useAppDispatch();

  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [employeeData, setEmployeeData] = useState<any[]>([]);

  const [createBulkEmployee, { isLoading }] = useCreateBulkEmployeeMutation();

  const [error, setError] = useState<string | undefined>(undefined);

  const columnDefs = columnFields.map((key) => ({
    headerName: key,
    field: key,
    sortable: true,
    filter: true,
    resizable: true,
  }));
  const [colDefs] = useState<ColDef<any>[]>(columnDefs);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(undefined);
    const file = event.target.files?.[0];
    if (!file) return;

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (result) => {
        const rawRows = result.data as any[];

        // Check if the uploaded file contains all required columns
        const uploadedHeaders = result.meta.fields ?? [];

        // Check for missing required headers
        const missingHeaders = requiredFields.filter(
          (field) => !uploadedHeaders.includes(field)
        );

        if (missingHeaders.length > 0) {
          setError(`Missing required columns: ${missingHeaders.join(", ")}`);
          return;
        }

        // Filter out completely empty rows
        const rows = rawRows.filter((row) => {
          return columnFields.some((field) => {
            const value = row[field];
            return value !== undefined && String(value).trim() !== "";
          });
        });

        if (!rows.length) {
          toast.error("No valid data rows found in CSV");
          return;
        }
        setEmployeeData(rows);
      },
    });
  };

  const handleSubmit = async () => {
    if (!employeeData.length) {
      toast.error("No data to upload");
      return;
    }

    // TODO: Replace with API submission logic
    console.log("Submitting data:", employeeData);

    const res = await createBulkEmployee(employeeData).unwrap();

    if (res.success) {
      toast(res.message);
      dispatch(modal.mutation.close());
    }

    toast("Data uploaded successfully!");

    setEmployeeData([]);
  };

  const handleClose = () => {
    setEmployeeData([]);
  };

  const handleDelete = (rowIndex: number) => {
    const updatedData = [...employeeData];
    updatedData.splice(rowIndex, 1);
    setEmployeeData(updatedData);
  };

  const handleDownloadTemplate = () => {
    const link = document.createElement("a");
    link.href = "/templates/create_employee_template.csv";
    link.setAttribute("download", "create-employee-template.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="sm:min-w-xl lg:min-w-2xl">
      <DialogHeader className="sticky top-0 !bg-white z-10 flex pt-6">
        <DialogTitle>Employee Bulk Upload</DialogTitle>
        <DialogDescription className="max-w-sm">
          <div>
            <p>Upload a `.csv` file with employee information.</p>
          </div>
          <div className="flex flex-col gap-4 mt-2 pb-2 z-40">
            <Input
              type="file"
              accept=".csv"
              ref={fileInputRef}
              // className="hidden"
              onChange={handleFileUpload}
              disabled={isLoading}
            />
            <Button
              onClick={handleDownloadTemplate}
              variant="link"
              className="px-0 cursor-pointer w-max"
            >
              Download Template
            </Button>
          </div>
        </DialogDescription>
      </DialogHeader>

      <div className="w-full">
        {error && (
          <div>
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}
        {employeeData.length > 0 && colDefs.length > 0 && (
          <div className="mt-4 max-h-[400px] overflow-auto border rounded">
            <BaseTable
              rowData={employeeData}
              columnDefs={colDefs}
              setPaginationPageSize={() => {}}
              showCustomPagination={false}
              actionOptions={{
                showDefault: true,
                actions: [
                  {
                    title: "Delete",
                    Icon: Trash2Icon,
                    onClick: (node) => {
                      handleDelete(node.rowIndex!);
                    },
                  },
                ],
              }}
            />
          </div>
        )}
      </div>

      {employeeData.length > 0 && colDefs.length > 0 && (
        <div className="flex justify-end gap-2 mt-4">
          <Button
            variant="outline"
            disabled={isLoading}
            size="sm"
            onClick={handleClose}
          >
            Cancel
          </Button>
          <Button disabled={isLoading} size="sm" onClick={handleSubmit}>
            Submit
          </Button>
        </div>
      )}
    </div>
  );
};

export default EmployeeBulkUploadModal;
