import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { useGetAccountDataQuery } from "@/services/account.service";
import { useSwitchCompanyMutation } from "@/services/auth.service";
import { account } from "@/store/module/account";
import { auth } from "@/store/module/auth";
import { company } from "@/store/module/company";
import { sheet } from "@/store/module/sheet";
import { skipToken } from "@reduxjs/toolkit/query";
import { PlusIcon } from "lucide-react";
import { Button } from "../ui/button";
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from "../ui/popover";

const CompanyDropdown = () => {
  const dispatch = useAppDispatch();

  const { data: companyData } = useAppSelector((state) => state.company);
  const { data: accountData } = useAppSelector((state) => state.account);

  const { data } = useGetAccountDataQuery(
    accountData?.slug ? { slug: accountData?.slug } : skipToken,
    { refetchOnMountOrArgChange: true }
  );

  const [switchBusiness] = useSwitchCompanyMutation();

  if (data) {
    dispatch(account.mutation.setAccount(data.data));
  }

  const handleSelectCompany = async (selectedCompany: {
    id: string;
    name: string;
    status: string;
    logo: string;
    registrationNumber: string;
  }) => {
    const res = await switchBusiness({
      companyId: selectedCompany.id,
    }).unwrap();
    dispatch(auth.mutation.setToken(res.data.access_token));
    dispatch(
      auth.mutation.setAccount({
        userEmail: res.data.email,
        userId: res.data.userId,
        userRoleId: res.data.roleId,
      })
    );
    dispatch(company.mutation.setCompany(selectedCompany));
    setTimeout(() => {
      window.location.reload();
    }, 500);
  };
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className="h-auto justify-start overflow-clip"
          variant="outline"
        >
          <img
            src={
              companyData?.logo ||
              "https://www.corebanknigeria.com/corebank.svg"
            }
            className="h-10 w-10 object-contain"
            alt="logo"
          />
          {companyData?.name}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start">
        <div className="flex flex-col gap-2">
          {accountData?.companies.map((ac) => (
            <PopoverClose className="w-full flex-1">
              <Button
                className="justify-start h-auto overflow-clip w-full"
                variant="outline"
                onClick={() => handleSelectCompany(ac)}
              >
                <img
                  src={
                    ac?.logo || "https://www.corebanknigeria.com/corebank.svg"
                  }
                  className="h-8 w-8 object-contain"
                  alt="logo"
                />
                {ac?.name}
              </Button>
            </PopoverClose>
          ))}
          <PopoverClose className="w-full flex-1">
            <Button
              className="overflow-clip w-full"
              variant="ghost"
              onClick={() =>
                dispatch(
                  sheet.mutation.open({
                    component: SheetConstant.createCompanySheet,
                  })
                )
              }
            >
              <PlusIcon />
              Add Company
            </Button>
          </PopoverClose>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default CompanyDropdown;
