import { useAppDispatch } from "@/hooks";
import {
  NameDescriptionDef,
  NameDescriptionSchema,
} from "@/models/validations";
import { useCreateRegionMutation } from "@/services/region.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { InputField, TextareaField } from "../form-fields";
import { Button } from "../ui/button";
import { Form } from "../ui/form";

const CreateRegionForm = () => {
  const dispatch = useAppDispatch();

  const [createRegion, { isLoading: isCreatingEmployee, error }] =
    useCreateRegionMutation();

  const form = useForm<NameDescriptionDef>({
    resolver: zodResolver(NameDescriptionSchema),
    defaultValues: {},
    mode: "all",
  });

  const onSubmit = async (data: NameDescriptionDef) => {
    try {
      const res = await createRegion({ ...data, description: "" }).unwrap();

      if (res.success) {
        toast("Request submitted and is pending authorization");
        dispatch(modal.mutation.close());
      }
    } catch {
      console.log(error);
    }
  };

  console.log(form.formState.errors);

  return (
    <section className="max-w-md mx-auto min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <>
              <InputField form={form} name="name" label="Name" />

              <TextareaField
                form={form}
                name="description"
                label="Description "
                placeholder="Enter unit description"
              />

              <div className="flex gap-4 justify-between">
                <Button className="font-medium" disabled={isCreatingEmployee}>
                  {isCreatingEmployee && (
                    <LoaderIcon className="animate-spin" />
                  )}
                  Create new region
                </Button>
              </div>
            </>
          </div>
        </form>
      </Form>
    </section>
  );
};

export default CreateRegionForm;
