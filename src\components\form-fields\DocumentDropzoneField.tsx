import { <PERSON>U<PERSON><PERSON>I<PERSON>, Trash2 } from "lucide-react";
import { Dispatch, SetStateAction, useState } from "react";
import Dropzone from "react-dropzone";
import {
  FieldError,
  FieldPath,
  FieldValues,
  Path,
  PathValue,
  UseFormReturn,
} from "react-hook-form";
import { toast } from "sonner";
import CropModal from "../modal/CropModal";
import { Button } from "../ui/button";
import { FormField, FormItem, FormLabel, FormMessage } from "../ui/form";

interface DocumentDropzoneFieldProps<T extends FieldValues> {
  form: UseFormReturn<T>;
  name: FieldPath<T>;
  error?: FieldError;
  label?: string;
  shouldCrop?: boolean;
  documents: {
    base64File: string;
    fileName: string;
    fileSize: number;
  }[];
  setDocuments: Dispatch<
    SetStateAction<
      {
        base64File: string;
        fileName: string;
        fileSize: number;
      }[]
    >
  >;
}

// Cloudinary constants (replace these with your actual credentials)
const CLOUDINARY_URL = "https://api.cloudinary.com/v1_1/ddqs87ym7/image/upload";
const UPLOAD_PRESET = "organa"; // Must be unsigned

const DocumentDropzoneField = <T extends FieldValues>({
  documents,
  setDocuments,
  form,
  name,
  label,
  shouldCrop = true,
}: DocumentDropzoneFieldProps<T>) => {
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);
  const [cropModalOpen, setCropModalOpen] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);

  const MAX_SIZE_MB = 5;
  const formatBytesToMB = (bytes: number): string =>
    `${(bytes / (1024 * 1024)).toFixed(2)} MB`;

  const handleDrop = async (files: File[]) => {
    const uploadedFile = files[0];

    const sizeInMB = uploadedFile.size / (1024 * 1024);
    if (sizeInMB > MAX_SIZE_MB) {
      toast.error(`File too large. Maximum size allowed is ${MAX_SIZE_MB} MB.`);
      return;
    }

    if (
      uploadedFile.name.toLowerCase() === documents[0]?.fileName.toLowerCase()
    ) {
      toast.error(
        "Duplicate file detected. Please rename the file if it's different."
      );
      return;
    }

    const reader = new FileReader();
    reader.onload = async () => {
      const base64 = reader.result as string;

      if (shouldCrop) {
        setImageToCrop(base64);
        setCropModalOpen(true);
      } else {
        try {
          const url = await uploadToCloudinary(uploadedFile);
          setDocuments([
            {
              base64File: url,
              fileName: uploadedFile.name,
              fileSize: uploadedFile.size,
            },
          ]);
          form.setValue(name, url as PathValue<T, Path<T>>);
          form.trigger(name);
        } catch (error) {
          console.error(error);
          toast.error("Upload failed");
        }
      }
    };

    reader.readAsDataURL(uploadedFile);
  };

  const handleRemove = (idx: number) => {
    const updatedDocuments = [...documents];
    updatedDocuments.splice(idx, 1);
    setDocuments(updatedDocuments);
    form.setValue(name, "" as PathValue<T, Path<T>>);
    form.trigger(name);
  };

  const uploadToCloudinary = async (file: Blob) => {
    return new Promise<string>((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      const formData = new FormData();
      formData.append("file", file);
      formData.append("upload_preset", UPLOAD_PRESET);

      xhr.open("POST", CLOUDINARY_URL);

      xhr.upload.addEventListener("progress", (event) => {
        if (event.lengthComputable) {
          const percent = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(percent);
        }
      });

      xhr.onload = () => {
        if (xhr.status === 200) {
          const response = JSON.parse(xhr.responseText);
          resolve(response.secure_url);
        } else {
          reject(new Error("Upload failed"));
        }
        setUploadProgress(null);
      };

      xhr.onerror = () => {
        setUploadProgress(null);
        reject(new Error("Upload error"));
      };

      xhr.send(formData);
    });
  };

  return (
    <FormField
      control={form.control}
      name={name}
      render={() => (
        <FormItem className="w-full">
          <div className="space-y-4">
            <div>
              {label && <FormLabel className="mb-2">{label}</FormLabel>}
              {documents[0] ? (
                <div className="mt-4 flex justify-between h-16 bg-white shadow border p-2 rounded-md">
                  <div className="flex items-center gap-3">
                    <img
                      src={documents[0].base64File}
                      alt={documents[0].fileName}
                      className="w-10 h-10 object-cover rounded-md shrink-0"
                    />
                    <div>
                      <p className="text-sm">
                        {documents[0].fileName ?? "Attachment 1"}
                      </p>
                      <p className="text-xs font-medium mt-0.5 text-neutral-500">
                        {formatBytesToMB(documents[0].fileSize)}
                      </p>
                    </div>
                  </div>

                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="hover:bg-destructive/40 hover:text-destructive text-destructive"
                    onClick={() => handleRemove(0)}
                  >
                    <Trash2 />
                  </Button>
                </div>
              ) : (
                <Dropzone
                  onDrop={handleDrop}
                  onDropRejected={(fileRejected) =>
                    toast.error(fileRejected[0].errors[0].message)
                  }
                  maxFiles={1}
                  multiple={false}
                  maxSize={1024 * 5000}
                  accept={{
                    "image/jpeg": [".jpg", ".jpeg"],
                    "image/png": [".png"],
                  }}
                >
                  {({ getRootProps, getInputProps }) => (
                    <section className="w-full border border-dashed rounded-md py-4 shadow">
                      <div
                        {...getRootProps()}
                        className="w-full h-full flex flex-col items-center justify-center cursor-pointer"
                      >
                        <input {...getInputProps()} />
                        <div className="w-full flex justify-center items-center flex-col rounded-md">
                          <div className="bg-white p-2 rounded-md shadow-sm">
                            <CloudUploadIcon className="w-6 text-primary h-6 shrink-0" />
                          </div>
                          <p className="text-center text-xs font-medium text-neutral-500 mt-4">
                            <span className="text-primary font-semibold">
                              Click to upload
                            </span>{" "}
                            or Drag and drop some files here
                          </p>
                          <p className="text-center text-xs text-neutral-500">
                            Max file size: 5MB
                          </p>
                        </div>
                      </div>
                    </section>
                  )}
                </Dropzone>
              )}
            </div>
            {uploadProgress !== null && (
              <p className="text-xs text-blue-600">
                Uploading: {uploadProgress}%
              </p>
            )}
          </div>
          <FormMessage />
          {imageToCrop && (
            <CropModal
              open={cropModalOpen}
              image={imageToCrop}
              onClose={() => setCropModalOpen(false)}
              onCropDone={async (croppedBlob) => {
                try {
                  const url = await uploadToCloudinary(croppedBlob);
                  setDocuments([
                    {
                      base64File: url,
                      fileName: "product-img.jpg",
                      fileSize: croppedBlob.size,
                    },
                  ]);
                  form.setValue(name, url as PathValue<T, Path<T>>);
                  form.trigger(name);
                } catch (error) {
                  console.error(error);
                  toast.error("Upload failed");
                } finally {
                  setCropModalOpen(false);
                }
              }}
            />
          )}
        </FormItem>
      )}
    />
  );
};

export default DocumentDropzoneField;
