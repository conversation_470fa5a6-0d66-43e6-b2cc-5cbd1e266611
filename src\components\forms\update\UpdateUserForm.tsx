import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ComboBoxField,
  Input<PERSON>ield,
} from "@/components/form-fields";
import MultiSelectField from "@/components/form-fields/MultiSelectField";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { UserResponseDef } from "@/models/response/users";
import {
  UpdateUserDef,
  UpdateUserSchema,
} from "@/models/validations/user/update-user.validation";
import { useGetBranchesQuery } from "@/services/branch.service";
import { useGetRolesQuery } from "@/services/role.service";
import { useUpdateUserMutation } from "@/services/user.service";
import { sheet } from "@/store/module/sheet";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

const UpdateUserForm = () => {
  const dispatch = useAppDispatch();

  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const { data: userData } = metadata as { data: UserResponseDef };

  const [updateUser, { isLoading: isCreatingUser, requestId }] =
    useUpdateUserMutation();

  const { data: roleRes, isLoading: isLoadingRoles } = useGetRolesQuery(
    undefined,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  // const { data: locationRes, isLoading: isLoadingLocation } =
  //   useGetLocationsQuery(undefined, {
  //     refetchOnMountOrArgChange: true,
  //   });

  const { data: branchRes, isLoading: isLoadingBranches } = useGetBranchesQuery(
    undefined,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  const form = useForm<UpdateUserDef>({
    resolver: zodResolver(UpdateUserSchema),
    defaultValues: {
      id: userData.id,
      branches: userData.branches ? userData.branches.map((br) => br.id) : [],
      email: userData.email,
      hasAccessToAllBranches: userData.hasAccessToAllBranches,
      roleId: userData.roleId,
    },
    mode: "all",
  });

  const onSubmit = async (data: UpdateUserDef) => {
    try {
      console.log(data);

      const res = await updateUser(data).unwrap();
      if (res.success) {
        toast("User request submitted and is pending authorization");
        dispatch(sheet.mutation.close());
      }
    } catch (error) {
      console.log(error);

      if (error && typeof error === "object" && "data" in error) {
        const errData = error.data as { message?: string };
        toast(errData.message ?? "Something went wrong.", { id: requestId });
      } else {
        toast("Something went wrong. Try again!", { id: requestId });
      }
    }
  };
  const hasAccessToAllBranches = form.watch("hasAccessToAllBranches");
  // const regionalUser = form.watch("regionalUser");

  useEffect(() => {
    if (hasAccessToAllBranches) {
      form.setValue("branches", []);
    }
  }, [hasAccessToAllBranches, form]);

  console.log(form.formState.errors);

  return (
    <section className=" min-[500px]:min-w-md p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <InputField
              form={form}
              name="email"
              label="Email"
              placeholder="Enter user email"
            />
            <ComboBoxField
              options={
                roleRes?.data
                  ? roleRes?.data.map((role) => ({
                      value: role.id,
                      label: role.name,
                    }))
                  : []
              }
              form={form}
              name="roleId"
              label="Role"
              placeholder="Select Role"
              isLoading={isLoadingRoles}
            />

            <MultiSelectField
              options={
                branchRes?.data
                  ? branchRes?.data.map((branch) => ({
                      value: branch.id,
                      label: branch.name,
                    }))
                  : []
              }
              form={form}
              disabled={hasAccessToAllBranches}
              name="branches"
              label="Branch"
              placeholder="Select Branch"
              isLoading={isLoadingBranches}
              description="Multiple Select is allowed"
            />
            {/* <CheckBoxField
              form={form}
              name="regionalUser"
              label="Regional User"
            /> */}
            {/* {regionalUser && (
              <ComboBoxField
                options={
                  locationRes?.data
                    ? locationRes?.data.map((location) => ({
                        value: location.id,
                        label: location.name,
                      }))
                    : []
                }
                form={form}
                name="locations"
                label="Location"
                placeholder="Select Location"
                isLoading={isLoadingLocation}
              />
            )} */}
            <CheckBoxField
              form={form}
              name="hasAccessToAllBranches"
              label="Has Access to all branches"
            />
          </div>
          <Button className="w-full font-medium" disabled={isCreatingUser}>
            {isCreatingUser && <LoaderIcon className="animate-spin" />}
            Update User
          </Button>
        </form>
      </Form>
    </section>
  );
};

export default UpdateUserForm;
