import { z } from "zod";

export const CreateCompanySchema = z.object({
  email: z.string().email("Invalid company email"),
  phoneNumber: z.string().min(1, "Phone number is required"), // use string instead of number for better UX in forms
  name: z.string().min(1, "Company name is required"),
  logo: z.string().url("Invalid logo URL").optional(),
  address: z.string().min(1, "Company address is required"),
  state: z.string().min(1, "State is required"),
  city: z.string().min(1, "City is required"),
  zipCode: z.string().optional(),
  website: z.string().optional(),
  description: z.string().optional(),
  registrationNumber: z.string().optional(),
  industry: z.string().min(1, "Industry is required"),
  industryType: z.string().min(1, "Industry type is required"),
  country: z.string().min(1, "Country is required"),
});

export type CreateCompanyDef = z.infer<typeof CreateCompanySchema>;
