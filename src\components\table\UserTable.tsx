import { DeleteEntityConstant } from "@/constants/DeleteEntityConstant";
import { ModalConstant } from "@/constants/ModalConstant";
import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { GradeResponseDef } from "@/models/response/grade";
import { UserResponseDef } from "@/models/response/users";
import { useGetUsersQuery } from "@/services/user.service";
import { modal } from "@/store/module/modal";
import { sheet } from "@/store/module/sheet";
import { ColDef, GridApi, IRowNode } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import BaseTable from "./BaseTable";

const UserTable = () => {
  const { data: response, isLoading } = useGetUsersQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<UserResponseDef>[]>([
    {
      headerName: "Email",
      field: "email",
    },
    {
      headerName: "Role",
      field: "role.name",
    },
    {
      headerName: "Branch",
      field: "branches",
      valueGetter: ({ node }) =>
        !node?.data?.hasAccessToAllBranches
          ? node?.data?.branches
            ? node.data.branches.map((br) => br.name).join(", ")
            : "-"
          : "All",
    },
    {
      headerName: "All Branches Access",
      field: "hasAccessToAllBranches",
      valueGetter: ({ node }) =>
        node?.data?.hasAccessToAllBranches ? "YES" : "NO",
    },
    {
      headerName: "Created by",
      field: "createdBy",
    },
    {
      headerName: "Status",
      field: "status",
    },
  ]);

  const onEditClick = (node: IRowNode<GradeResponseDef>) => {
    dispatch(
      sheet.mutation.open({
        component: SheetConstant.updateUserSheet,
        metadata: {
          data: node.data,
        },
      })
    );
  };

  const onDeleteClick = async ({
    node,
  }: {
    node: IRowNode<UserResponseDef>;
    api: GridApi;
  }) => {
    if (!node.data) {
      toast.error("User not found");
    }
    dispatch(
      modal.mutation.open({
        open: true,
        modalType: ModalConstant.confirmationModal,
        metadata: {
          id: node.data?.id,
          entity: DeleteEntityConstant.users,
          title: `Delete user "${node.data?.email}"?`,
          description: `Are you sure you want to delete this user "${node.data?.email}"`,
          warning:
            "By deleting this user, this user will also lose access to this account",
        },
      })
    );
  };
  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={response?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        defaultEditAction: onEditClick,
        defaultDeleteAction: onDeleteClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default UserTable;
