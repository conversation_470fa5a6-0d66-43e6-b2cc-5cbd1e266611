import EmployeeTableHeaderActions from "@/components/headers/EmployeeTableHeaderActions";
import TaxJurisdictionTableHeaderActions from "@/components/headers/TaxJurisdictionTableHeaderActions";
import { ModalConstant } from "@/constants/ModalConstant";
import { RouteConstant } from "@/constants/RouteConstant";
import {
  AuthorizationQueueView,
  BatchView,
  BranchView,
  ContractTypeView,
  DashboardView,
  DepartmentView,
  DesignationView,
  EmployeeGroupView,
  EmployeeView,
  GradeView,
  PayrollView,
  RoleView,
  SalaryPackageView,
  SubBranchView,
  TaxJurisdictionView,
  UserView,
} from "@/view";
import RegionView from "@/view/RegionView";
import { RouteDef } from ".";

export const dashboardRoute: RouteDef[] = [
  {
    path: RouteConstant.dashboard.path,
    Component: DashboardView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
    },
  },
  {
    path: RouteConstant.employees.path,
    Component: EmployeeView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Employee Management",
        addNewEntityBtnLabel: "Add Employee",
        addNewEntityModal: ModalConstant.createEmployeeModal,
        ExtraActionsBtn: EmployeeTableHeaderActions,
      },
    },
  },
  {
    path: RouteConstant.grade.path,
    Component: GradeView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Grade Management",
        addNewEntityBtnLabel: "Add Grade",
        addNewEntityModal: ModalConstant.gradeModal,
      },
    },
  },
  {
    path: RouteConstant.cadre.path,
    Component: GradeView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Cadre Management",
        addNewEntityBtnLabel: "Add Cadre",
        addNewEntityModal: ModalConstant.gradeModal,
      },
    },
  },
  {
    path: RouteConstant.department.path,
    Component: DepartmentView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Department Management",
        addNewEntityBtnLabel: "Add Department",
        addNewEntityModal: ModalConstant.departmentModal,
      },
    },
  },
  // {
  //   path: RouteConstant.departmentUnit.path,
  //   Component: DepartmentView,
  //   metadata: {
  //     hasSidebar: true,
  //     isAuthenticated: true,
  //     dashboardProps: {
  //       heading: "Units/Department Management",
  //       addNewEntityBtnLabel: "Add Unit/Department",
  //       addNewEntityModal: ModalConstant.departmentModal,
  //     },
  //   },
  // },
  {
    path: RouteConstant.subBranch.path,
    Component: SubBranchView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Sub Branch Management",
        addNewEntityBtnLabel: "Add Sub-Branch",
        addNewEntityModal: ModalConstant.subBranchModal,
      },
    },
  },
  {
    path: RouteConstant.taxJurisdiction.path,
    Component: TaxJurisdictionView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Tax Jurisdiction Management",
        addNewEntityBtnLabel: "Add Tax Jurisdiction",
        addNewEntityModal: ModalConstant.taxJurisdictionModal,
        ExtraActionsBtn: TaxJurisdictionTableHeaderActions,
      },
    },
  },
  {
    path: RouteConstant.regions.path,
    Component: RegionView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Region Management",
        addNewEntityBtnLabel: "Add Region",
        addNewEntityModal: ModalConstant.regionModal,
      },
    },
  },
  {
    path: RouteConstant.gradeLevel.path,
    Component: EmployeeGroupView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Grade Level Management",
        addNewEntityBtnLabel: "Add Grade Level",
        addNewEntityModal: ModalConstant.createEmployeeGroupModal,
      },
    },
  },
  {
    path: RouteConstant.contractType.path,
    Component: ContractTypeView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Employment Type Management",
        addNewEntityBtnLabel: "Add Employment Type",
        addNewEntityModal: ModalConstant.contractTypeModal,
      },
    },
  },
  {
    path: RouteConstant.designation.path,
    Component: DesignationView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Designation Management",
        addNewEntityBtnLabel: "Add Designation",
        addNewEntityModal: ModalConstant.designationModal,
      },
    },
  },
  {
    path: RouteConstant.branch.path,
    Component: BranchView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Branch/Unit Management",
        addNewEntityBtnLabel: "Add Branch/Unit",
        addNewEntityModal: ModalConstant.createBranchModal,
      },
    },
  },
  {
    path: RouteConstant.roles.path,
    Component: RoleView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Role Management",
        addNewEntityBtnLabel: "Add Role",
        addNewEntityModal: ModalConstant.createRoleModal,
      },
    },
  },
  {
    path: RouteConstant.authorization.path,
    Component: AuthorizationQueueView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Authorization Management",
      },
    },
  },
  {
    path: RouteConstant.users.path,
    Component: UserView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "User Management",
        addNewEntityBtnLabel: "Add User",
        addNewEntityModal: ModalConstant.userModal,
      },
    },
  },
  {
    path: RouteConstant.salaryPackage.path,
    Component: SalaryPackageView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Salary Package Management",
        addNewEntityBtnLabel: "Salary Package",
        addNewEntityModal: ModalConstant.salaryPackageModal,
      },
    },
  },
  {
    path: RouteConstant.payroll.path,
    Component: PayrollView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Payroll Management",
        addNewEntityBtnLabel: "Upload payroll",
        addNewEntityModal: ModalConstant.payroll,
      },
    },
  },
  {
    path: RouteConstant.batch.path,
    Component: BatchView,
    metadata: {
      hasSidebar: true,
      isAuthenticated: true,
      dashboardProps: {
        heading: "Batch Management",
      },
    },
  },
];
