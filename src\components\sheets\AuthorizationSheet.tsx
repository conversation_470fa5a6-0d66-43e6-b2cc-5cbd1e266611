import { useAppDispatch, useAppSelector } from "@/hooks";
import { isValidJSON } from "@/lib/utils";
import { AuthorizationResponseDef } from "@/models/response/authorization";
import { useAcceptRequestMutation } from "@/services/authorization.service";
import { sheet } from "@/store/module/sheet";
import { useRef } from "react";
import { toast } from "sonner";
import CollapsibleItem from "../shared/CollapsibleItem";
import { Button } from "../ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { ScrollArea, ScrollBar } from "../ui/scroll-area";
import { SheetDescription, SheetHeader, SheetTitle } from "../ui/sheet";

const AuthorizationSheet = () => {
  const dispatch = useAppDispatch();
  const [acceptRequest, { isLoading }] = useAcceptRequestMutation();
  const rejectFormCloseBtnRef = useRef<HTMLButtonElement>(null);

  const {
    sheetOptions: { metadata },
  } = useAppSelector((state) => state.sheet);

  const checkerData = metadata?.data as AuthorizationResponseDef;

  if (!checkerData) {
    return (
      <div>
        <p>Data not found</p>
      </div>
    );
  }

  const handleAccept = async () => {
    try {
      const res = await acceptRequest({ id: checkerData.id }).unwrap();
      toast(res.message);
      dispatch(sheet.mutation.close());
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <>
      <SheetHeader className="pb-0">
        <SheetTitle className="font-medium text-left !text-base">
          {`${checkerData.action} - ${checkerData.module.replaceAll("_", " ")}`}
        </SheetTitle>
        <SheetDescription>{checkerData.status}</SheetDescription>

        {checkerData.status === "PENDING" && (
          <div className="flex gap-3">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" disabled={isLoading} variant="destructive">
                  Reject
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="min-w-80 p-2">
                {/* <RejectAuthorizationForm
                  id={checkerData.checkerQueueId}
                  closeBtnRef={rejectFormCloseBtnRef}
                /> */}
                <DropdownMenuItem className="w-full">
                  <Button
                    variant="outline"
                    className="w-full"
                    ref={rejectFormCloseBtnRef}
                    size="sm"
                  >
                    Cancel
                  </Button>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button size="sm" disabled={isLoading} onClick={handleAccept}>
              Accept
            </Button>
          </div>
        )}
      </SheetHeader>

      <ScrollArea className="h-[calc(100%-10rem)] border-t mt-4">
        <div className="p-4">
          <p className="text-sm font-medium">
            {`"${checkerData.requestedBy}" request to ${
              checkerData.action
            } ${checkerData.module.replaceAll(
              "_",
              " "
            )} with the following request data:`}
          </p>

          <div className="space-y-4 mt-4">
            {isValidJSON(checkerData.data as string) ? (
              Object.entries(JSON.parse(checkerData.data as string)).map(
                ([key, value]) => (
                  <CollapsibleItem key={key} label={key} value={value} />
                )
              )
            ) : (
              <p className="text-sm text-muted-foreground">
                {checkerData?.toString()}
              </p>
            )}
          </div>
        </div>
        <ScrollBar orientation="vertical" />
      </ScrollArea>
    </>
  );
};

export default AuthorizationSheet;
