"use client";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { ModalConstant } from "@/constants/ModalConstant";
import { RouteConstant } from "@/constants/RouteConstant";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { cn } from "@/lib/utils";
import { auth } from "@/store/module/auth";
import { modal } from "@/store/module/modal";
import {
  BadgeCheckIcon,
  BanknoteIcon,
  BarChartIcon,
  BoxesIcon,
  BriefcaseIcon,
  Building2Icon,
  ChevronDownIcon,
  ChevronRightIcon,
  ChevronsUpDownIcon,
  FileSignatureIcon,
  FolderKanbanIcon,
  LayoutDashboardIcon,
  LockIcon,
  LogOutIcon,
  MapPinIcon,
  MonitorIcon,
  ReceiptTextIcon,
  ShieldCheckIcon,
  SlidersHorizontalIcon,
  Users,
  UsersIcon,
  WalletIcon,
} from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import CompanyDropdown from "../dropdown/CompanyDropdown";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { ScrollArea, ScrollBar } from "../ui/scroll-area";
import SidebarToggler from "./SidebarToggler";

const AppSidebar = () => {
  const dispatch = useAppDispatch();
  const { pathname } = useLocation();
  const { open } = useSidebar();

  const { account } = useAppSelector((state) => state.auth);

  const {
    modalOptions: { open: modalOpen },
  } = useAppSelector((state) => state.modal);

  const sideBarLinks = [
    {
      to: RouteConstant.dashboard.path,
      Icon: LayoutDashboardIcon,
      label: "Dashboard",
    },

    {
      label: "Employee Management",
      Icon: UsersIcon,
      items: [
        {
          to: RouteConstant.employees.path,
          label: "Employees",
          Icon: Users,
        },
        {
          to: RouteConstant.designation.path,
          label: "Designation",
          Icon: BriefcaseIcon,
        },
        {
          to: RouteConstant.contractType.path,
          label: "Employment Type",
          Icon: FileSignatureIcon,
        },
        {
          to: RouteConstant.batch.path,
          label: "Batch",
          Icon: FolderKanbanIcon,
        },
      ],
    },

    {
      label: "Payroll & Finance",
      Icon: BanknoteIcon,
      items: [
        {
          to: RouteConstant.salaryPackage.path,
          label: "Salary Package",
          Icon: BanknoteIcon,
        },
        {
          to: RouteConstant.payroll.path,
          label: "Payroll",
          Icon: ReceiptTextIcon,
        },
        {
          to: RouteConstant.pfa.path,
          label: "PFA",
          Icon: WalletIcon,
        },
      ],
    },

    {
      label: `Cadre & Grade Level`,
      Icon: BarChartIcon,
      items: [
        {
          to: RouteConstant.cadre.path,
          label: "Cadre",
          Icon: BarChartIcon,
        },
        {
          to: RouteConstant.gradeLevel.path,
          label: "Grade Level",
          Icon: SlidersHorizontalIcon,
        },
      ],
    },

    {
      label: "Organization Setup",
      Icon: Building2Icon,
      items: [
        {
          to: RouteConstant.regions.path,
          label: "Regions",
          Icon: BoxesIcon,
        },
        {
          to: RouteConstant.taxJurisdiction.path,
          label: "Tax Jurisdiction",
          Icon: BoxesIcon,
        },
        {
          to: RouteConstant.department.path,
          label: "Department",
          Icon: Building2Icon,
        },
        {
          to: RouteConstant.branch.path,
          label: "Branch/Unit",
          Icon: MapPinIcon,
        },
        {
          to: RouteConstant.subBranch.path,
          label: "Sub Branch",
          Icon: Building2Icon,
        },
      ],
    },

    {
      label: "System Administration",
      Icon: ShieldCheckIcon,
      items: [
        {
          to: RouteConstant.users.path,
          label: "Users",
          Icon: MonitorIcon,
        },
        {
          to: RouteConstant.roles.path,
          label: "Roles",
          Icon: BadgeCheckIcon,
        },
        {
          to: RouteConstant.authorization.path,
          label: "Authorization Queue",
          Icon: ShieldCheckIcon,
        },
      ],
    },
  ];

  console.log(pathname);

  return (
    <>
      {!open && (
        <SidebarToggler
          className="bg-white border border-brand/10 shadow-md text-brand top-11 "
          Icon={ChevronRightIcon}
        />
      )}
      <Sidebar className="">
        <SidebarHeader className="ps-5  relative">
          <CompanyDropdown />
        </SidebarHeader>

        <SidebarContent>
          <ScrollArea className="h-[100vh-100px]">
            <SidebarGroup>
              <SidebarGroupContent className="list-none ">
                {sideBarLinks.map(({ label, items, to }) =>
                  items ? (
                    <Collapsible
                      defaultOpen
                      className="group/collapsible"
                      key={label}
                    >
                      <SidebarGroup>
                        <SidebarGroupLabel asChild>
                          <CollapsibleTrigger
                            className={cn(
                              "!text-sm !font-normal flex items-center px-3 py-2 h-10 rounded-md",
                              items.some(({ to }) => pathname === to) &&
                                "!bg-primary text-white"
                            )}
                          >
                            {label}
                            <ChevronDownIcon className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
                          </CollapsibleTrigger>
                        </SidebarGroupLabel>
                        <CollapsibleContent>
                          <SidebarGroupContent className="list-none mt-2 ml-1">
                            {items.map(({ label, to }) => (
                              <SidebarMenuItem
                                key={label}
                                className={cn(
                                  "border-l-4 border-transparent ",
                                  pathname === to
                                    ? "bg-primary/10 text-brand border-primary hover:bg-sky-50 hover:text-brand hover:border-primary"
                                    : "hover:border-sidebar-accent"
                                )}
                              >
                                <SidebarMenuButton className="h-auto p-0">
                                  <Link
                                    to={to}
                                    className="flex items-center gap-2  w-full py-3 px-3 text-xs"
                                  >
                                    {/* <Icon className="w-4 h-4" /> */}
                                    {label}
                                  </Link>
                                </SidebarMenuButton>
                              </SidebarMenuItem>
                            ))}
                          </SidebarGroupContent>
                        </CollapsibleContent>
                      </SidebarGroup>
                    </Collapsible>
                  ) : (
                    <SidebarMenuItem
                      key={label}
                      className={cn(
                        "border-l-4 border-transparent ",
                        pathname.replace("-", " ").includes(label.toLowerCase())
                          ? "bg-brand/5 text-brand border-brand hover:bg-brand/5 hover:text-brand hover:border-brand"
                          : "hover:border-sidebar-accent"
                      )}
                    >
                      <SidebarMenuButton className="h-auto p-0">
                        <Link
                          to={to}
                          className="flex items-center gap-2  w-full py-3 px-3 text-xs"
                        >
                          {/* <Icon className="w-4 h-4" /> */}
                          {label}
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  )
                )}
              </SidebarGroupContent>
            </SidebarGroup>
            <ScrollBar orientation="vertical" />
          </ScrollArea>
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <DropdownMenu modal={!modalOpen}>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton className="border h-auto">
                    <div className="flex justify-between w-full pt-2">
                      <div className="flex flex-col flex-1 text-left text-sm leading-tight">
                        <span className="truncate font-semibold">
                          {`${account?.userFullName}`}
                        </span>
                        <span className="truncate text-xs">
                          {account?.userEmail}
                        </span>
                      </div>
                      <ChevronsUpDownIcon className="ml-auto size-4" />
                    </div>
                  </SidebarMenuButton>
                </DropdownMenuTrigger>

                <DropdownMenuContent
                  side="top"
                  align="start"
                  sideOffset={10}
                  className="flex gap-1 flex-col w-60"
                >
                  <SidebarMenuButton
                    className="data-[state=open]:bg-sidebar-accent cursor-pointer shrink-0 w-full data-[state=open]:text-sidebar-accent-foreground"
                    onClick={() =>
                      dispatch(
                        modal.mutation.open({
                          modalType: ModalConstant.changePasswordModal,
                        })
                      )
                    }
                  >
                    <LockIcon className="size-3" />
                    Change Password
                  </SidebarMenuButton>
                  <SidebarMenuButton
                    className="data-[state=open]:bg-sidebar-accent cursor-pointer shrink-0 w-full data-[state=open]:text-sidebar-accent-foreground"
                    onClick={() => dispatch(auth.mutation.setToken(null))}
                  >
                    <LogOutIcon className="size-3" />
                    Logout
                  </SidebarMenuButton>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>
    </>
  );
};

export default AppSidebar;
