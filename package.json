{"name": "sundrycustomerportal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@lottiefiles/dotlottie-react": "^0.13.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.0", "@reduxjs/toolkit": "^2.6.1", "@tailwindcss/vite": "^4.1.3", "ag-grid-community": "^33.2.4", "ag-grid-react": "^33.2.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "form": "^0.2.5", "input-otp": "^1.4.2", "libphonenumber-js": "^1.12.7", "localforage": "^1.10.0", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "papaparse": "^5.5.2", "path": "^0.12.7", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-easy-crop": "^5.4.1", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-json-view": "^1.21.3", "react-redux": "^9.2.0", "react-router-dom": "^7.5.0", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "sonner": "^2.0.3", "sundrycustomerportal": "file:", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.14.1", "@types/papaparse": "^5.3.15", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}