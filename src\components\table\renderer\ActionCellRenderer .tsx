import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { GridApi, ICellRendererParams, IRowNode } from "ag-grid-community";
import { Edit2Icon, MoreHorizontal, Trash2Icon } from "lucide-react";
import { useCallback } from "react";

export type ActionCellProps = {
  actions?: {
    onClick: (node: IRowNode) => void;
    title: string;
    Icon: React.JSX.ElementType;
    className?: string;
  }[];
  showDefault?: boolean;
  defaultEditAction?: (node: IRowNode) => void;
  defaultDeleteAction?: ({
    api,
    node,
  }: {
    node: IRowNode;
    api: GridApi;
  }) => Promise<void>;
  isLoading?: boolean;
};

const ActionCellRenderer = (
  props: ICellRendererParams & { actionOptions?: ActionCellProps }
) => {
  const { api, node } = props;
  const handleManageClick = () => {
    if (props.actionOptions?.defaultEditAction) {
      const {
        actionOptions: { defaultEditAction },
      } = props;
      return defaultEditAction(node);
    }
    return;
  };

  const onDeleteClick = useCallback(async () => {
    if (props.actionOptions?.defaultDeleteAction) {
      await props.actionOptions.defaultDeleteAction({ api, node });
    }
  }, [props.actionOptions, api, node]);

  return (
    <div className="flex items-center space-x-2 mt-0.5">
      <Popover>
        <PopoverTrigger asChild>
          <Button disabled={props.actionOptions?.isLoading} variant="ghost">
            <MoreHorizontal />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          align="start"
          alignOffset={-80}
          className="min-w-40 w-max *:w-full  flex flex-col p-0 border border-neutral-300 shadow-md"
        >
          {props.actionOptions &&
            props.actionOptions?.actions &&
            props.actionOptions?.actions.map((act) => (
              <>
                <Button
                  onClick={() => act.onClick(props.node)}
                  variant="ghost"
                  size="sm"
                  className=" text-left justify-start text-xs
                w-32"
                  disabled={props.actionOptions?.isLoading}
                  key={act.title}
                >
                  <act.Icon className="!w-3 !h-3" />
                  {act.title}
                </Button>
              </>
            ))}
          {props.actionOptions && props.actionOptions?.showDefault && (
            <>
              {props.actionOptions.defaultEditAction && (
                <Button
                  onClick={handleManageClick}
                  variant="ghost"
                  size="sm"
                  className=" text-left justify-start text-xs
              w-32"
                  disabled={props.actionOptions?.isLoading}
                >
                  <Edit2Icon className="!w-3 !h-3" />
                  Manage
                </Button>
              )}
              {props.actionOptions.defaultDeleteAction && (
                <Button
                  onClick={onDeleteClick}
                  variant="ghost"
                  size="sm"
                  disabled={props.actionOptions?.isLoading}
                  className=" text-left justify-start w-32 text-xs text-destructive hover:text-destructive/80"
                >
                  <Trash2Icon className="!w-3 !h-3" />
                  Delete
                </Button>
              )}
            </>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default ActionCellRenderer;
