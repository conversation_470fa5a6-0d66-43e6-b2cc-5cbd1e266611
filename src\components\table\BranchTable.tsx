import { DeleteEntityConstant } from "@/constants/DeleteEntityConstant";
import { ModalConstant } from "@/constants/ModalConstant";
import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { BranchResponseDef } from "@/models/response/branch";
import { useGetBranchesQuery } from "@/services/branch.service";
import { modal } from "@/store/module/modal";
import { sheet } from "@/store/module/sheet";
import { ColDef, GridApi, IRowNode } from "ag-grid-community";
import { EyeIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import BaseTable from "./BaseTable";

const BranchTable = () => {
  const { data: branches, isLoading } = useGetBranchesQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<BranchResponseDef>[]>([
    {
      headerName: "Name",
      field: "name",
    },
    {
      headerName: "Tax Jurisdiction",
      field: "taxJurisdiction",
    },
    {
      headerName: "Created At",
      field: "createdAt",
    },
    {
      headerName: "Status",
      field: "status",
    },
  ]);

  const onEditClick = (node: IRowNode<BranchResponseDef>) => {
    dispatch(
      sheet.mutation.open({
        component: SheetConstant.updateBranchSheet,
        metadata: {
          data: node.data,
        },
      })
    );
  };

  const onDeleteClick = async ({
    node,
  }: {
    node: IRowNode<BranchResponseDef>;
    api: GridApi;
  }) => {
    if (!node.data) {
      toast.error("Branch not found");
    }
    dispatch(
      modal.mutation.open({
        open: true,
        modalType: ModalConstant.confirmationModal,
        metadata: {
          id: node.data?.id,
          entity: DeleteEntityConstant.branch,
          title: `Delete "${node.data?.name}" branch?`,
          description: `Are you sure you want to delete this branch "${node.data?.name}"`,
          warning:
            "By deleting this branch, the associated user will lose access to this account.",
        },
      })
    );
  };
  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={branches?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        defaultEditAction: onEditClick,
        defaultDeleteAction: onDeleteClick,
        showDefault: true,
        actions: [
          {
            title: "View",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default BranchTable;
