import { SheetConstant } from "@/constants/SheetConstant";
import { useAppDispatch } from "@/hooks";
import { PayrollUploadResponseDef } from "@/models/response/payroll/read-payroll.response";
import { useGetPayrollUploadsQuery } from "@/services/payroll.service";
import { sheet } from "@/store/module/sheet";
import { ColDef } from "ag-grid-community";
import { EyeIcon, LayersIcon } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import BaseTable from "./BaseTable";

const PayrollTable = () => {
  const { data, isLoading, error, requestId } = useGetPayrollUploadsQuery(
    undefined,
    {
      refetchOnMountOrArgChange: true,
      refetchOnReconnect: true,
    }
  );

  if (error && typeof error === "object" && "data" in error) {
    const errData = error.data as { message?: string };
    toast(errData.message ?? "Something went wrong.", { id: requestId });
  }

  const dispatch = useAppDispatch();

  const [colDefs] = useState<ColDef<PayrollUploadResponseDef>[]>([
    {
      headerName: "Period",
      field: "period",
    },
    {
      headerName: "Uploaded By",
      field: "uploadedBy",
    },
    {
      headerName: "Approved By",
      field: "approvedBy",
    },
  ]);

  return (
    <BaseTable
      columnDefs={colDefs}
      rowData={data?.data || []}
      setPaginationPageSize={() => {}}
      loading={isLoading}
      actionOptions={{
        showDefault: true,
        actions: [
          {
            title: "View Record",
            Icon: EyeIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.entityViewSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
          {
            title: "View Batch",
            Icon: LayersIcon,
            onClick: (node) => {
              dispatch(
                sheet.mutation.open({
                  component: SheetConstant.payrollBatchSheet,
                  metadata: {
                    data: {
                      ...node.data,
                    },
                  },
                })
              );
            },
          },
        ],
      }}
    />
  );
};

export default PayrollTable;
