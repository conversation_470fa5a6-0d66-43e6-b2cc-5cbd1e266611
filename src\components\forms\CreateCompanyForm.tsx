import { industries } from "@/data/industries";
import { industryType } from "@/data/industry-type";
import { stateLga } from "@/data/state-lga";
import { useAppDispatch } from "@/hooks";
import {
  CreateCompanyDef,
  CreateCompanySchema,
} from "@/models/validations/company/create-company.validation";
import { useCreateCompanyMutation } from "@/services/company.service";
import { sheet } from "@/store/module/sheet";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { ComboBoxField, InputField } from "../form-fields";
import DocumentDropzoneField from "../form-fields/DocumentDropzoneField";
import PhoneInputField from "../form-fields/PhoneInputField";
import { Button } from "../ui/button";
import { Form } from "../ui/form";

const CreateCompanyForm = () => {
  const dispatch = useAppDispatch();
  const [createCompany, { isLoading, isError, error }] =
    useCreateCompanyMutation();

  const [logos, setLogos] = useState<
    {
      base64File: string;
      fileName: string;
      fileSize: number;
    }[]
  >([]);

  if (isError) {
    toast(JSON.stringify(error));
  }

  const form = useForm<CreateCompanyDef>({
    resolver: zodResolver(CreateCompanySchema),
    defaultValues: {
      country: "Nigeria",
    },
    mode: "all",
  });

  const state = form.watch("state");

  // Reset email and password when the user type changes

  const onSubmit = async (data: CreateCompanyDef) => {
    const res = await createCompany({
      ...data,
    }).unwrap();

    if (res.success) {
      dispatch(sheet.mutation.close());
    }
  };

  console.log(form.formState.errors);

  return (
    <>
      <section className="w-full">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4  p-8"
          >
            <div className="space-y-4">
              <InputField
                form={form}
                name="email"
                label="Email"
                placeholder="Email"
              />
              <InputField
                form={form}
                name="name"
                label="Name"
                placeholder="Company name"
              />
              <InputField
                form={form}
                name="address"
                label="Address"
                placeholder="Address"
              />

              <ComboBoxField
                options={Object.keys(stateLga).map((state) => ({
                  name: state,
                }))}
                form={form}
                labelKey="name"
                valueKey="name"
                name="state"
                label="State"
                placeholder="State"
              />
              <ComboBoxField
                options={
                  state
                    ? stateLga[state as keyof typeof stateLga].map((city) => ({
                        name: city,
                      }))
                    : []
                }
                disabled={!state}
                form={form}
                labelKey="name"
                valueKey="name"
                name="city"
                label="City"
                placeholder="City"
              />

              {/* <ComboBoxField
                  options={countries}
                  labelKey="name"
                  valueKey="name"
                  form={form}
                  // disabled
                  name="country"
                  label="Country"
                  placeholder="Country"
                /> */}

              <InputField
                form={form}
                name="zipCode"
                label="ZIP Code"
                optional
                placeholder="ZIP Code"
              />
              <PhoneInputField
                form={form}
                name="phoneNumber"
                label="Phone Number"
              />
              <ComboBoxField
                options={industries}
                labelKey="name"
                valueKey="name"
                form={form}
                name="industry"
                label="Industry"
                placeholder="Select Industry"
              />
              <ComboBoxField
                options={industryType}
                labelKey="label"
                valueKey="key"
                form={form}
                name="industryType"
                label="Industry Type"
                placeholder="Select Industry Type"
              />
              <DocumentDropzoneField
                form={form}
                name={"logo"}
                label="Logo"
                documents={logos}
                setDocuments={setLogos}
                shouldCrop={false}
              />
              <InputField
                form={form}
                name="website"
                label="Website"
                optional
                placeholder="Website"
              />
              <InputField
                form={form}
                name="registrationNumber"
                label="Registration Number"
                optional
                placeholder="Registration Number"
              />
              <InputField
                form={form}
                name="description"
                label="Description"
                optional
                placeholder="Description"
              />
              <Button disabled={isLoading}>
                {isLoading && <LoaderIcon className="animate-spin" />}
                Create company
              </Button>
            </div>
          </form>
        </Form>
      </section>
    </>
  );
};

export default CreateCompanyForm;
