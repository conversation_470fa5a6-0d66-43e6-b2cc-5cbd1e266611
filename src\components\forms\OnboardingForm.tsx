import { ModalConstant } from "@/constants/ModalConstant";
import { industries } from "@/data/industries";
import { industryType } from "@/data/industry-type";
import { stateLga } from "@/data/state-lga";
import { useAppDispatch } from "@/hooks";
import {
  OnboardingDef,
  OnboardingSchema,
} from "@/models/validations/auth/onboarding.validation";
import { useInitiateEnrollmentMutation } from "@/services/enrollment.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useState } from "react";
import { FieldPath, useForm } from "react-hook-form";
import { toast } from "sonner";
import { ComboBoxField, InputField } from "../form-fields";
import DocumentDropzoneField from "../form-fields/DocumentDropzoneField";
import PhoneInput<PERSON>ield from "../form-fields/PhoneInputField";
import AuthFormHeader from "../sections/AuthFormHeader";
import { But<PERSON> } from "../ui/button";
import { Form } from "../ui/form";

const headers = [
  {
    title: "Create your account",
    description: "Enter your email and name to get started.",
  },
  {
    title: "Create your first company",
    description: "Tell us more about your company to continue.",
  },
  {
    title: "Set a secure password",
    description: "Protect your account with a strong password.",
  },
];

const OnboardingForm = () => {
  const dispatch = useAppDispatch();
  const [initiateEnrollment, { isLoading }] = useInitiateEnrollmentMutation();

  const [currentStep, setCurrentStep] = useState(0);

  const [logos, setLogos] = useState<
    {
      base64File: string;
      fileName: string;
      fileSize: number;
    }[]
  >([]);

  const form = useForm<OnboardingDef>({
    resolver: zodResolver(OnboardingSchema),
    defaultValues: {
      company: {
        country: "Nigeria",
      },
    },
    mode: "all",
  });

  const state = form.watch("company.state");

  // Reset email and password when the user type changes

  const onSubmit = async (data: OnboardingDef) => {
    const res = await initiateEnrollment({
      ...data.company,
      accountEmail: data.account.email,
      accountName: data.account.name,
      password: data.user.password,
    }).unwrap();

    if (res.data.uniqueRef) {
      toast.success("Enrollment initiated successfully");
      dispatch(
        modal.mutation.open({
          modalType: ModalConstant.onboardingOtpModal,
          metadata: {
            uniqueRef: res.data.uniqueRef,
            email: data.account.email,
            otpRef: res.data.otpRef,
          },
        })
      );
    }
  };

  //  Handle step change
  const handleStepChange = async ({
    step,
    fields,
  }: {
    step: number;
    fields?: FieldPath<OnboardingDef>[];
  }) => {
    if (step < 0) {
      return;
    }
    if (step > currentStep) {
      const isValid = await form.trigger(fields);

      if (!isValid) {
        return;
      }
    }
    setCurrentStep(step);
  };

  return (
    <>
      <section className="max-w-md mx-auto min-h-screen place-content-center">
        <AuthFormHeader
          title={headers[currentStep].title}
          description={headers[currentStep].description}
          showImg={false}
        />

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4 bg-white shadow-md border rounded-md p-8"
          >
            {currentStep === 0 && (
              <div className="space-y-4">
                <InputField
                  form={form}
                  name="account.email"
                  label="Email"
                  placeholder="Enter account email"
                />
                <InputField
                  form={form}
                  name="account.name"
                  label="Name"
                  placeholder="Enter account name"
                />

                <Button
                  type="button"
                  className="w-full font-medium"
                  disabled={isLoading}
                  onClick={() => {
                    handleStepChange({
                      step: 1,
                      fields: ["account.email", "account.name"],
                    });
                  }}
                >
                  {isLoading && <LoaderIcon className="animate-spin" />}
                  Proceed
                </Button>
              </div>
            )}
            {currentStep === 1 && (
              <div className="space-y-4">
                <InputField
                  form={form}
                  name="company.email"
                  label="Email"
                  placeholder="Email"
                />
                <InputField
                  form={form}
                  name="company.name"
                  label="Name"
                  placeholder="Company name"
                />
                <InputField
                  form={form}
                  name="company.address"
                  label="Address"
                  placeholder="Address"
                />

                <ComboBoxField
                  options={Object.keys(stateLga).map((state) => ({
                    name: state,
                  }))}
                  form={form}
                  labelKey="name"
                  valueKey="name"
                  name="company.state"
                  label="State"
                  placeholder="State"
                />
                <ComboBoxField
                  options={
                    state
                      ? stateLga[state as keyof typeof stateLga].map(
                          (city) => ({
                            name: city,
                          })
                        )
                      : []
                  }
                  disabled={!state}
                  form={form}
                  labelKey="name"
                  valueKey="name"
                  name="company.city"
                  label="City"
                  placeholder="City"
                />

                {/* <ComboBoxField
                  options={countries}
                  labelKey="name"
                  valueKey="name"
                  form={form}
                  // disabled
                  name="company.country"
                  label="Country"
                  placeholder="Country"
                /> */}

                <InputField
                  form={form}
                  name="company.zipCode"
                  label="ZIP Code"
                  optional
                  placeholder="ZIP Code"
                />
                <PhoneInputField
                  form={form}
                  name="company.phoneNumber"
                  label="Phone Number"
                />
                <ComboBoxField
                  options={industries}
                  labelKey="name"
                  valueKey="name"
                  form={form}
                  name="company.industry"
                  label="Industry"
                  placeholder="Select Industry"
                />
                <ComboBoxField
                  options={industryType}
                  labelKey="label"
                  valueKey="key"
                  form={form}
                  name="company.industryType"
                  label="Industry Type"
                  placeholder="Select Industry Type"
                />
                <DocumentDropzoneField
                  form={form}
                  name={"company.logo"}
                  label="Logo"
                  documents={logos}
                  setDocuments={setLogos}
                  shouldCrop={false}
                />
                <InputField
                  form={form}
                  name="company.website"
                  label="Website"
                  optional
                  placeholder="Website"
                />
                <InputField
                  form={form}
                  name="company.registrationNumber"
                  label="Registration Number"
                  optional
                  placeholder="Registration Number"
                />
                <InputField
                  form={form}
                  name="company.description"
                  label="Description"
                  optional
                  placeholder="Description"
                />
                <Button
                  type="button"
                  className="w-full font-medium"
                  disabled={isLoading}
                  onClick={() => {
                    handleStepChange({
                      step: 2,
                      fields: [
                        "company.email",
                        "company.name",
                        "company.country",
                        "company.state",
                        "company.city",
                        "company.address",
                        "company.phoneNumber",
                        "company.industry",
                        "company.industryType",
                        "company.website",
                        "company.phoneNumber",
                        "company.registrationNumber",
                        "company.description",
                        "company.zipCode",
                        "company.logo",
                      ],
                    });
                  }}
                >
                  {isLoading && <LoaderIcon className="animate-spin" />}
                  Proceed
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(0)}
                  className="w-full font-medium"
                  disabled={isLoading}
                >
                  Go back
                </Button>
              </div>
            )}

            {currentStep === 2 && (
              <div className="space-y-4">
                <InputField
                  form={form}
                  name="user.password"
                  label="Password"
                  type="password"
                  placeholder="••••••••"
                />
                <InputField
                  form={form}
                  name="user.confirmPassword"
                  label="Confirm Password"
                  type="password"
                  placeholder="Enter password again"
                />
                <Button className="w-full font-medium" disabled={isLoading}>
                  {isLoading && <LoaderIcon className="animate-spin" />}
                  Proceed
                </Button>
                <Button
                  className="w-full font-medium"
                  disabled={isLoading}
                  type="button"
                  onClick={() => setCurrentStep(0)}
                  variant="outline"
                >
                  Go Back
                </Button>
              </div>
            )}
          </form>
        </Form>
      </section>
    </>
  );
};

export default OnboardingForm;
