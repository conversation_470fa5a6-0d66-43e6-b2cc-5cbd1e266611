import { useAppDispatch } from "@/hooks";
import {
  CreateGradeDef,
  CreateGradeSchema,
} from "@/models/validations/grade/create-grade.validation";
import { useCreateGradeMutation } from "@/services/grade.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { InputField, TextareaField } from "../form-fields";
import { Button } from "../ui/button";
import { Form } from "../ui/form";

const CreateGradeForm = () => {
  const dispatch = useAppDispatch();

  const [createGrade, { isLoading: isCreatingRole, error, requestId }] =
    useCreateGradeMutation();

  const form = useForm<CreateGradeDef>({
    resolver: zodResolver(CreateGradeSchema),
    defaultValues: {},
    mode: "all",
  });

  const onSubmit = async (data: CreateGradeDef) => {
    try {
      const res = await createGrade(data).unwrap();

      if (res.success) {
        toast("Grade request submitted and is pending authorization");
        dispatch(modal.mutation.close());
      }
    } catch {
      if (error && typeof error === "object" && "data" in error) {
        const errData = error.data as { message?: string };
        toast(errData.message ?? "Something went wrong.", { id: requestId });
      } else {
        toast("Something went wrong. Try again!", { id: requestId });
      }
    }
  };

  return (
    <section className="max-w-md mx-auto min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            <InputField
              form={form}
              name="name"
              label="Title"
              placeholder="Enter role title"
            />
            <TextareaField
              form={form}
              name="description"
              label="Description "
              placeholder="Enter role description"
            />
          </div>
          <Button className="w-full font-medium" disabled={isCreatingRole}>
            {isCreatingRole && <LoaderIcon className="animate-spin" />}
            {`Create Cadre`}
          </Button>
        </form>
      </Form>
    </section>
  );
};

export default CreateGradeForm;
